package de.dasjeff.aSMPVCore;

import com.google.inject.Inject;
import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.event.proxy.ProxyInitializeEvent;
import com.velocitypowered.api.event.proxy.ProxyShutdownEvent;
import com.velocitypowered.api.plugin.Plugin;
import com.velocitypowered.api.plugin.annotation.DataDirectory;
import com.velocitypowered.api.proxy.ProxyServer;
import de.dasjeff.aSMPVCore.database.PlayerDataAccessor;
import de.dasjeff.aSMPVCore.managers.ConfigManager;
import de.dasjeff.aSMPVCore.managers.DatabaseManager;
import de.dasjeff.aSMPVCore.managers.CacheManager;
import de.dasjeff.aSMPVCore.managers.PlayerDataManager;
import de.dasjeff.aSMPVCore.managers.ModuleManager;
import org.slf4j.Logger;

import java.nio.file.Path;

@Plugin(
        id = "asmpvcore",
        name = "ASMPVCore",
        version = "@version@", // This will be replaced by Gradle
        description = "Velocity Core System for aSMP",
        authors = {"DasJeff"}
)
public class ASMPVCore {

    private final ProxyServer server;
    private final Logger logger;
    private final Path dataDirectory;

    private ConfigManager configManager;
    private DatabaseManager databaseManager;
    private CacheManager cacheManager;
    private PlayerDataManager playerDataManager;
    private ModuleManager moduleManager;

    @Inject
    public ASMPVCore(ProxyServer server, Logger logger, @DataDirectory Path dataDirectory) {
        this.server = server;
        this.logger = logger;
        this.dataDirectory = dataDirectory;
    }

    @Subscribe
    public void onProxyInitialization(ProxyInitializeEvent event) {
        logger.info("Initializing ASMPVCore v@version@...");

        // 1. ConfigManager
        try {
            configManager = new ConfigManager(this, dataDirectory);
            configManager.loadConfig();
            logger.info("ConfigManager initialized.");
        } catch (Exception e) {
            logger.error("Failed to initialize ConfigManager!", e);
            // Critical failure, might prevent other managers from working
            return;
        }

        // 2. DatabaseManager
        if (configManager.getBoolean("database.enabled", true)) {
            try {
                databaseManager = new DatabaseManager(this);
                if (!databaseManager.initialize()) {
                    logger.error("DatabaseManager failed to initialize. Check configuration. Plugin might not function correctly.");
                    // Depending on how critical DB is, you might disable the plugin or parts of it
                } else {
                    logger.info("DatabaseManager initialized.");
                }
            } catch (Exception e) {
                logger.error("Failed to initialize DatabaseManager!", e);
            }
        } else {
            logger.info("Database is disabled in configuration.");
        }

        // 3. CacheManager (Redis and Caffeine)
        try {
            cacheManager = new CacheManager(this);
            logger.info("CacheManager initialized.");
        } catch (Exception e) {
            logger.error("Failed to initialize CacheManager!", e);
        }

        // 4. PlayerDataManager & PlayerDataAccessor
        if (databaseManager != null && databaseManager.isConnected()) {
            PlayerDataAccessor playerDataAccessor = new PlayerDataAccessor(this);
            playerDataManager = new PlayerDataManager(this, playerDataAccessor);
            logger.info("PlayerDataManager initialized.");
        } else {
            logger.warn("PlayerDataManager could not be initialized because DatabaseManager is not available.");
        }

        // 5. ModuleManager
        moduleManager = new ModuleManager(this);
        // TODO: Register modules here, e.g., moduleManager.registerModule(new PunishmentModule(this));
        moduleManager.loadModules();
        moduleManager.enableModules();
        logger.info("ModuleManager initialized and modules processed.");

        logger.info("ASMPVCore has been enabled successfully!");
    }

    @Subscribe
    public void onProxyShutdown(ProxyShutdownEvent event) {
        logger.info("Disabling ASMPVCore...");

        if (moduleManager != null) {
            moduleManager.disableModules();
        }
        if (cacheManager != null) {
            cacheManager.shutdown();
        }
        if (databaseManager != null) {
            databaseManager.closeDataSource();
        }

        logger.info("ASMPVCore has been disabled.");
    }

    // Getters for managers and core components
    public ProxyServer getServer() { return server; }
    public Logger getLogger() { return logger; }
    public Path getDataDirectory() { return dataDirectory; }
    public ConfigManager getConfigManager() { return configManager; }
    public DatabaseManager getDatabaseManager() { return databaseManager; }
    public CacheManager getCacheManager() { return cacheManager; }
    public PlayerDataManager getPlayerDataManager() { return playerDataManager; }
    public ModuleManager getModuleManager() { return moduleManager; }
}
