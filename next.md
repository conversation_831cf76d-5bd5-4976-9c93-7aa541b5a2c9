diff --git a/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/CacheManager.java b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/CacheManager.java
new file mode 100644
index 0000000..5121111
--- /dev/null
+++ b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/CacheManager.java
@@ -0,0 +1,135 @@
+package de.dasjeff.aSMPVCore.managers;
+
+import com.github.benmanes.caffeine.cache.Cache;
+import com.github.benmanes.caffeine.cache.Caffeine;
+import de.dasjeff.aSMPVCore.ASMPVCore;
+import de.dasjeff.aSMPVCore.lib.jedis.Jedis;
+import de.dasjeff.aSMPVCore.lib.jedis.JedisPool;
+import de.dasjeff.aSMPVCore.lib.jedis.JedisPoolConfig;
+import org.jetbrains.annotations.NotNull;
+import org.jetbrains.annotations.Nullable;
+
+import java.time.Duration;
+import java.util.Map;
+import java.util.concurrent.ConcurrentHashMap;
+import java.util.concurrent.TimeUnit;
+import java.util.function.Function;
+
+public class CacheManager {
+
+    private final ASMPVCore plugin;
+    private final ConcurrentMap<String, Cache<Object, Object>> localCaches = new ConcurrentHashMap<>();
+    private JedisPool jedisPool;
+    private final boolean redisEnabled;
+
+    public CacheManager(ASMPVCore plugin) {
+        this.plugin = plugin;
+
+        this.redisEnabled = plugin.getConfigManager().getBoolean("redis.enabled", false);
+        if (redisEnabled) {
+            initializeRedis();
+        }
+    }
+
+    private void initializeRedis() {
+        String host = plugin.getConfigManager().getString("redis.host", "localhost");
+        int port = plugin.getConfigManager().getInt("redis.port", 6379);
+        String password = plugin.getConfigManager().getString("redis.password", "");
+        int timeout = plugin.getConfigManager().getInt("redis.timeout", 2000);
+
+        JedisPoolConfig poolConfig = new JedisPoolConfig();
+        poolConfig.setMaxTotal(plugin.getConfigManager().getInt("redis.pool.maxTotal", 128));
+        poolConfig.setMaxIdle(plugin.getConfigManager().getInt("redis.pool.maxIdle", 128));
+        poolConfig.setMinIdle(plugin.getConfigManager().getInt("redis.pool.minIdle", 16));
+        poolConfig.setTestOnBorrow(plugin.getConfigManager().getBoolean("redis.pool.testOnBorrow", true));
+        poolConfig.setTestOnReturn(plugin.getConfigManager().getBoolean("redis.pool.testOnReturn", true));
+        poolConfig.setTestWhileIdle(plugin.getConfigManager().getBoolean("redis.pool.testWhileIdle", true));
+        poolConfig.setMinEvictableIdleTime(Duration.ofMillis(plugin.getConfigManager().getLong("redis.pool.minEvictableIdleTimeMillis", 60000)));
+        poolConfig.setTimeBetweenEvictionRuns(Duration.ofMillis(plugin.getConfigManager().getLong("redis.pool.timeBetweenEvictionRunsMillis", 30000)));
+        poolConfig.setNumTestsPerEvictionRun(3);
+        poolConfig.setBlockWhenExhausted(true);
+
+
+        if (password == null || password.trim().isEmpty()) {
+            jedisPool = new JedisPool(poolConfig, host, port, timeout);
+        } else {
+            jedisPool = new JedisPool(poolConfig, host, port, timeout, password);
+        }
+
+        try (Jedis jedis = jedisPool.getResource()) {
+            jedis.ping();
+            plugin.getLogger().info("Successfully connected to Redis server at " + host + ":" + port);
+        } catch (Exception e) {
+            plugin.getLogger().error("Failed to connect to Redis server. Distributed caching will be unavailable.", e);
+            jedisPool.close();
+            jedisPool = null;
+        }
+    }
+
+    /**
+     * Creates or retrieves a local Caffeine cache.
+     */
+    @SuppressWarnings("unchecked")
+    public <K, V> Cache<K, V> getOrCreateLocalCache(@NotNull String cacheName,
+                                                     long maximumSize,
+                                                     long expireAfterAccess,
+                                                     @NotNull TimeUnit timeUnit) {
+        return (Cache<K, V>) localCaches.computeIfAbsent(cacheName, name -> {
+            Caffeine<Object, Object> builder = Caffeine.newBuilder()
+                    .maximumSize(maximumSize)
+                    .expireAfterAccess(expireAfterAccess, timeUnit);
+            // Add .recordStats() if needed, based on a config option
+            return builder.build();
+        });
+    }
+
+    @Nullable
+    public <K, V> V getLocalCacheValue(@NotNull String cacheName, @NotNull K key, @NotNull Function<? super K, ? extends V> mappingFunction) {
+        Cache<K, V> cache = (Cache<K, V>) localCaches.get(cacheName);
+        if (cache == null) {
+            plugin.getLogger().warn("Attempted to access non-existent local cache: " + cacheName);
+            return mappingFunction.apply(key); // Fallback
+        }
+        return cache.get(key, mappingFunction);
+    }
+
+    public <K, V> void putLocalCacheValue(@NotNull String cacheName, @NotNull K key, @NotNull V value) {
+        Cache<K, V> cache = (Cache<K, V>) localCaches.get(cacheName);
+        if (cache == null) {
+            plugin.getLogger().warn("Attempted to put into non-existent local cache: " + cacheName);
+            return;
+        }
+        cache.put(key, value);
+    }
+
+    /**
+     * Provides access to a Jedis resource from the pool.
+     * Must be closed by the caller in a try-with-resources block.
+     * @return A Jedis instance, or null if Redis is disabled or unavailable.
+     */
+    @Nullable
+    public Jedis getJedisResource() {
+        if (!redisEnabled || jedisPool == null || jedisPool.isClosed()) {
+            return null;
+        }
+        try {
+            return jedisPool.getResource();
+        } catch (Exception e) {
+            plugin.getLogger().error("Could not get Jedis resource from pool", e);
+            return null;
+        }
+    }
+
+    public boolean isRedisEnabled() {
+        return redisEnabled && jedisPool != null && !jedisPool.isClosed();
+    }
+
+    public void shutdown() {
+        localCaches.values().forEach(Cache::invalidateAll);
+        localCaches.clear();
+        if (jedisPool != null && !jedisPool.isClosed()) {
+            jedisPool.close();
+            plugin.getLogger().info("Redis connection pool closed.");
+        }
+        plugin.getLogger().info("CacheManager shutdown complete.");
+    }
+}
diff --git a/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/ConfigManager.java b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/ConfigManager.java
new file mode 100644
index 0000000..4000802
--- /dev/null
+++ b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/ConfigManager.java
@@ -0,0 +1,118 @@
+package de.dasjeff.aSMPVCore.managers;
+
+import de.dasjeff.aSMPVCore.ASMPVCore;
+import de.dasjeff.aSMPVCore.lib.snakeyaml.Yaml;
+import org.jetbrains.annotations.Nullable;
+
+import java.io.File;
+import java.io.IOException;
+import java.io.InputStream;
+import java.io.OutputStream;
+import java.nio.file.Files;
+import java.nio.file.Path;
+import java.util.Collections;
+import java.util.HashMap;
+import java.util.List;
+import java.util.Map;
+
+public class ConfigManager {
+
+    private final ASMPVCore plugin;
+    private final Path dataDirectory;
+    private File configFile;
+    private Map<String, Object> configData;
+    private final Yaml yaml = new Yaml();
+
+    public ConfigManager(ASMPVCore plugin, Path dataDirectory) {
+        this.plugin = plugin;
+        this.dataDirectory = dataDirectory;
+        this.configFile = dataDirectory.resolve("config.yml").toFile();
+    }
+
+    public void loadConfig() {
+        if (!configFile.exists()) {
+            saveDefaultConfig();
+        }
+        try (InputStream in = Files.newInputStream(configFile.toPath())) {
+            configData = yaml.load(in);
+            if (configData == null) {
+                configData = new HashMap<>(); // Ensure not null if file is empty
+                plugin.getLogger().warn("config.yml was empty or malformed. Loaded empty configuration.");
+            }
+        } catch (IOException e) {
+            plugin.getLogger().error("Could not load config.yml", e);
+            configData = new HashMap<>(); // Default to empty map on error
+        }
+    }
+
+    public void saveDefaultConfig() {
+        if (!Files.exists(dataDirectory)) {
+            try {
+                Files.createDirectories(dataDirectory);
+            } catch (IOException e) {
+                plugin.getLogger().error("Could not create data directory: " + dataDirectory, e);
+                return;
+            }
+        }
+
+        try (InputStream defaultConfigStream = getClass().getClassLoader().getResourceAsStream("config.yml")) {
+            if (defaultConfigStream == null) {
+                plugin.getLogger().error("Default config.yml not found in JAR resources.");
+                return;
+            }
+            Files.copy(defaultConfigStream, configFile.toPath());
+            plugin.getLogger().info("Saved default config.yml to " + configFile.getAbsolutePath());
+        } catch (IOException e) {
+            // It might already exist, which is fine if we are just ensuring it's there.
+            // If it's a real error, log it.
+            if (!configFile.exists()) {
+                 plugin.getLogger().error("Could not save default config.yml", e);
+            }
+        }
+    }
+
+    @Nullable
+    private Object get(String path) {
+        if (configData == null) return null;
+        String[] parts = path.split("\\.");
+        Map<String, Object> currentMap = configData;
+        for (int i = 0; i < parts.length; i++) {
+            Object val = currentMap.get(parts[i]);
+            if (i == parts.length - 1) {
+                return val;
+            }
+            if (val instanceof Map) {
+                currentMap = (Map<String, Object>) val;
+            } else {
+                return null;
+            }
+        }
+        return null;
+    }
+
+    public String getString(String path, String def) {
+        Object val = get(path);
+        return (val instanceof String) ? (String) val : def;
+    }
+
+    public int getInt(String path, int def) {
+        Object val = get(path);
+        return (val instanceof Number) ? ((Number) val).intValue() : def;
+    }
+
+    public long getLong(String path, long def) {
+        Object val = get(path);
+        return (val instanceof Number) ? ((Number) val).longValue() : def;
+    }
+
+    public boolean getBoolean(String path, boolean def) {
+        Object val = get(path);
+        return (val instanceof Boolean) ? (Boolean) val : def;
+    }
+
+    public List<String> getStringList(String path) {
+        Object val = get(path);
+        return (val instanceof List) ? (List<String>) val : Collections.emptyList();
+    }
+}
diff --git a/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/DatabaseManager.java b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/DatabaseManager.java
new file mode 100644
index 0000000..861507d
--- /dev/null
+++ b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/DatabaseManager.java
@@ -0,0 +1,106 @@
+package de.dasjeff.aSMPVCore.managers;
+
+import de.dasjeff.aSMPVCore.ASMPVCore;
+import de.dasjeff.aSMPVCore.lib.hikaricp.HikariConfig;
+import de.dasjeff.aSMPVCore.lib.hikaricp.HikariDataSource;
+
+import java.sql.Connection;
+import java.sql.SQLException;
+
+public class DatabaseManager {
+
+    private final ASMPVCore plugin;
+    private HikariDataSource dataSource;
+    private final String host;
+    private final int port;
+    private final String database;
+    private final String username;
+    private final String password;
+    private final boolean useSSL;
+    private final int poolSize;
+    private final long connectionTimeout;
+    private final long idleTimeout;
+    private final long maxLifetime;
+
+    public DatabaseManager(ASMPVCore plugin) {
+        this.plugin = plugin;
+        ConfigManager config = plugin.getConfigManager();
+
+        this.host = config.getString("database.host", "localhost");
+        this.port = config.getInt("database.port", 3306);
+        this.database = config.getString("database.database", "adventuresmp_core");
+        this.username = config.getString("database.username", "root");
+        this.password = config.getString("database.password", "your_password");
+        this.useSSL = config.getBoolean("database.useSSL", false);
+
+        this.poolSize = config.getInt("database.poolSize", 10);
+        this.connectionTimeout = config.getLong("database.connectionTimeout", 30000);
+        this.idleTimeout = config.getLong("database.idleTimeout", 600000);
+        this.maxLifetime = config.getLong("database.maxLifetime", 1800000);
+    }
+
+    public boolean initialize() {
+        boolean defaultConfigUsed = "localhost".equals(host) &&
+                                    "root".equals(username) &&
+                                    "your_password".equals(password) &&
+                                    "adventuresmp_core".equals(database);
+
+        if (defaultConfigUsed) {
+            plugin.getLogger().error("==================================================");
+            plugin.getLogger().error("ASMPVCore DETECTED DEFAULT DATABASE CONFIGURATION!");
+            plugin.getLogger().error("Please configure your database settings in plugins/aSMPVCore/config.yml");
+            plugin.getLogger().error("Ensure the database server is running.");
+            plugin.getLogger().error("Database connection will not be established with default values.");
+            plugin.getLogger().error("==================================================");
+            return false; // Do not attempt to connect with default credentials
+        }
+
+        setupDataSource();
+        return isConnected();
+    }
+
+    private void setupDataSource() {
+        HikariConfig hikariConfig = new HikariConfig();
+        // Use the relocated driver class name
+        String driverClassName = "de.dasjeff.aSMPVCore.lib.mariadb.jdbc.Driver"; // Note: MariaDB driver class is usually org.mariadb.jdbc.Driver
+                                                                                  // The relocation might need to be specific.
+                                                                                  // For `org.mariadb.jdbc` relocated to `de.dasjeff.aSMPVCore.lib.mariadb`,
+                                                                                  // the class would be `de.dasjeff.aSMPVCore.lib.mariadb.Driver`.
+        hikariConfig.setDriverClassName(driverClassName); // Ensure this matches your relocation
+        hikariConfig.setJdbcUrl(String.format("****************************************************",
+                host, port, database, useSSL));
+        hikariConfig.setUsername(username);
+        hikariConfig.setPassword(password);
+
+        hikariConfig.setMaximumPoolSize(poolSize);
+        hikariConfig.setConnectionTimeout(connectionTimeout);
+        hikariConfig.setIdleTimeout(idleTimeout);
+        hikariConfig.setMaxLifetime(maxLifetime);
+
+        // Common HikariCP optimizations
+        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
+        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
+        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
+
+        try {
+            this.dataSource = new HikariDataSource(hikariConfig);
+            plugin.getLogger().info("Successfully established database connection pool to " + host + ":" + port + "/" + database);
+        } catch (Exception e) {
+            plugin.getLogger().error("Could not establish database connection pool! Database features will be unavailable.", e);
+            this.dataSource = null;
+        }
+    }
+
+    public Connection getConnection() throws SQLException {
+        if (dataSource == null) {
+            throw new SQLException("Database source is not available. Check plugin logs for connection errors.");
+        }
+        return dataSource.getConnection();
+    }
+
+    public boolean isConnected() {
+        return dataSource != null && !dataSource.isClosed();
+    }
+
+    public void closeDataSource() {
+        if (dataSource != null && !dataSource.isClosed()) {
+            dataSource.close();
+            plugin.getLogger().info("Database connection pool closed.");
+        }
+    }
+}
diff --git a/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/ModuleManager.java b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/ModuleManager.java
new file mode 100644
index 0000000..757294a
--- /dev/null
+++ b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/ModuleManager.java
@@ -0,0 +1,90 @@
+package de.dasjeff.aSMPVCore.managers;
+
+import de.dasjeff.aSMPVCore.ASMPVCore;
+import de.dasjeff.aSMPVCore.modules.IVModule;
+
+import java.util.LinkedHashMap;
+import java.util.Map;
+import java.util.Optional;
+
+public class ModuleManager {
+
+    private final ASMPVCore plugin;
+    private final Map<String, IVModule> modules = new LinkedHashMap<>();
+
+    public ModuleManager(ASMPVCore plugin) {
+        this.plugin = plugin;
+    }
+
+    public void registerModule(IVModule module) {
+        if (modules.containsKey(module.getName().toLowerCase())) {
+            plugin.getLogger().warn("Module with name '" + module.getName() + "' is already registered. Skipping.");
+            return;
+        }
+        modules.put(module.getName().toLowerCase(), module);
+        plugin.getLogger().info("Registered module: " + module.getName());
+    }
+
+    public void loadModules() {
+        plugin.getLogger().info("Loading all registered modules...");
+        for (IVModule module : modules.values()) {
+            try {
+                module.onLoad(plugin);
+                plugin.getLogger().info("Module loaded: " + module.getName());
+            } catch (Exception e) {
+                plugin.getLogger().error("Error loading module: " + module.getName(), e);
+            }
+        }
+        plugin.getLogger().info("All registered modules have been processed for loading.");
+    }
+
+    public void enableModules() {
+        plugin.getLogger().info("Enabling all registered modules...");
+        for (IVModule module : modules.values()) {
+            try {
+                plugin.getLogger().info("Enabling module: " + module.getName() + "...");
+                module.onEnable(plugin);
+                plugin.getLogger().info("Module enabled: " + module.getName());
+            } catch (Exception e) {
+                plugin.getLogger().error("Error enabling module: " + module.getName(), e);
+            }
+        }
+        plugin.getLogger().info("All registered modules have been processed for enabling.");
+    }
+
+    public void disableModules() {
+        plugin.getLogger().info("Disabling all registered modules...");
+        // Disable in reverse order of registration/enablement
+        IVModule[] modulesArray = modules.values().toArray(new IVModule[0]);
+        for (int i = modulesArray.length - 1; i >= 0; i--) {
+            IVModule module = modulesArray[i];
+            try {
+                plugin.getLogger().info("Disabling module: " + module.getName() + "...");
+                module.onDisable(plugin);
+                plugin.getLogger().info("Module disabled: " + module.getName());
+            } catch (Exception e) {
+                plugin.getLogger().error("Error disabling module: " + module.getName(), e);
+            }
+        }
+        plugin.getLogger().info("All registered modules have been processed for disabling.");
+        modules.clear(); // Clear after disabling all
+    }
+
+    public Optional<IVModule> getModule(String name) {
+        return Optional.ofNullable(modules.get(name.toLowerCase()));
+    }
+
+    public <T extends IVModule> Optional<T> getModule(Class<T> moduleClass) {
+        return modules.values().stream()
+                .filter(moduleClass::isInstance)
+                .map(moduleClass::cast)
+                .findFirst();
+    }
+
+    public Map<String, IVModule> getAllModules() {
+        return new LinkedHashMap<>(modules); // Return a copy
+    }
+
+    // TODO: Add reloadModule and reloadAllModules if needed, similar to Paper core's ModuleManager
+}
diff --git a/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/PlayerDataManager.java b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/PlayerDataManager.java
new file mode 100644
index 0000000..7806368
--- /dev/null
+++ b/aSMPVCore/src/main/java/de/dasjeff/aSMPVCore/managers/PlayerDataManager.java
@@ -0,0 +1,73 @@
+package de.dasjeff.aSMPVCore.managers;
+
+import com.github.benmanes.caffeine.cache.Cache;
+import com.velocitypowered.api.proxy.Player;
+import de.dasjeff.aSMPVCore.ASMPVCore;
+import de.dasjeff.aSMPVCore.database.PlayerDataAccessor;
+import de.dasjeff.aSMPVCore.model.PlayerData;
+import org.jetbrains.annotations.NotNull;
+import org.jetbrains.annotations.Nullable;
+
+import java.util.UUID;
+import java.util.concurrent.CompletableFuture;
+import java.util.concurrent.TimeUnit;
+
+public class PlayerDataManager {
+
+    private final ASMPVCore plugin;
+    private final PlayerDataAccessor dataAccessor;
+    private final Cache<UUID, PlayerData> playerDataCache;
+
+    private static final String PLAYER_DATA_CACHE_NAME = "playerDataCache";
+
+    public PlayerDataManager(ASMPVCore plugin, PlayerDataAccessor dataAccessor) {
+        this.plugin = plugin;
+        this.dataAccessor = dataAccessor;
+
+        this.playerDataCache = plugin.getCacheManager().getOrCreateLocalCache(
+                PLAYER_DATA_CACHE_NAME,
+                plugin.getConfigManager().getInt("cache.playerData.maxSize", 500),
+                plugin.getConfigManager().getLong("cache.playerData.expireMinutes", 30),
+                TimeUnit.MINUTES
+        );
+    }
+
+    /**
+     * Called when a player joins the proxy. Updates their data in the database
+     * and pre-loads it into the cache.
+     */
+    public void handlePlayerJoin(Player player) {
+        CompletableFuture.runAsync(() -> {
+            dataAccessor.updatePlayerData(player.getUniqueId(), player.getUsername());
+            // Pre-load/refresh cache
+            getPlayerDataAsync(player.getUniqueId());
+        }).exceptionally(ex -> {
+            plugin.getLogger().error("Error handling player join for " + player.getUsername(), ex);
+            return null;
+        });
+    }
+
+    /**
+     * Retrieves PlayerData, first from cache, then from database if not found.
+     * The result from the database will be cached.
+     */
+    public CompletableFuture<PlayerData> getPlayerDataAsync(@NotNull UUID playerUuid) {
+        PlayerData cachedData = playerDataCache.getIfPresent(playerUuid);
+        if (cachedData != null) {
+            return CompletableFuture.completedFuture(cachedData);
+        }
+
+        return CompletableFuture.supplyAsync(() -> {
+            PlayerData dbData = dataAccessor.getPlayerData(playerUuid);
+            if (dbData != null) {
+                playerDataCache.put(playerUuid, dbData);
+            }
+            return dbData;
+        });
+    }
+
+    public void invalidateCache(@NotNull UUID playerUuid) {
+        playerDataCache.invalidate(playerUuid);
+    }
+
+}