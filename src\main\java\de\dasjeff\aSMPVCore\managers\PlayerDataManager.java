package de.dasjeff.aSMPVCore.managers;

import com.github.benmanes.caffeine.cache.Cache;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.database.PlayerDataAccessor;
import de.dasjeff.aSMPVCore.model.PlayerData;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Manager for player data operations with caching support
 */
public class PlayerDataManager {

    private final ASMPVCore plugin;
    private final PlayerDataAccessor dataAccessor;
    private final Cache<UUID, PlayerData> playerDataCache;

    public PlayerDataManager(ASMPVCore plugin, PlayerDataAccessor dataAccessor) {
        this.plugin = plugin;
        this.dataAccessor = dataAccessor;
        this.playerDataCache = plugin.getCacheManager().getCache("player_data");
    }

    /**
     * Updates player data asynchronously
     */
    public CompletableFuture<Void> updatePlayerDataAsync(@NotNull UUID playerUuid, @NotNull String playerName) {
        return CompletableFuture.runAsync(() -> {
            try {
                dataAccessor.updatePlayerData(playerUuid, playerName);
                // Invalidate cache to force refresh on next access
                if (playerDataCache != null) {
                    playerDataCache.invalidate(playerUuid);
                }
            } catch (Exception e) {
                plugin.getLogger().error("Error updating player data for " + playerName, e);
            }
        });
    }

    /**
     * Gets player data with caching support
     */
    @Nullable
    public PlayerData getPlayerData(@NotNull UUID playerUuid) {
        if (playerDataCache != null) {
            return playerDataCache.get(playerUuid, uuid -> dataAccessor.getPlayerData(uuid));
        } else {
            return dataAccessor.getPlayerData(playerUuid);
        }
    }

    /**
     * Gets player data asynchronously
     */
    public CompletableFuture<PlayerData> getPlayerDataAsync(@NotNull UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> getPlayerData(playerUuid));
    }

    /**
     * Gets player UUID by name
     */
    @Nullable
    public UUID getPlayerUuidByName(@NotNull String playerName) {
        return dataAccessor.getPlayerUuidByName(playerName);
    }

    /**
     * Gets player UUID by name asynchronously
     */
    public CompletableFuture<UUID> getPlayerUuidByNameAsync(@NotNull String playerName) {
        return CompletableFuture.supplyAsync(() -> getPlayerUuidByName(playerName));
    }

    /**
     * Gets player name by UUID
     */
    @Nullable
    public String getPlayerNameByUuid(@NotNull UUID playerUuid) {
        PlayerData playerData = getPlayerData(playerUuid);
        return playerData != null ? playerData.getLastKnownName() : null;
    }

    /**
     * Gets player name by UUID asynchronously
     */
    public CompletableFuture<String> getPlayerNameByUuidAsync(@NotNull UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> getPlayerNameByUuid(playerUuid));
    }

    /**
     * Invalidates cached player data
     */
    public void invalidatePlayerData(@NotNull UUID playerUuid) {
        if (playerDataCache != null) {
            playerDataCache.invalidate(playerUuid);
        }
    }

    /**
     * Preloads player data into cache
     */
    public CompletableFuture<Void> preloadPlayerDataAsync(@NotNull UUID playerUuid) {
        return CompletableFuture.runAsync(() -> {
            try {
                PlayerData playerData = dataAccessor.getPlayerData(playerUuid);
                if (playerData != null && playerDataCache != null) {
                    playerDataCache.put(playerUuid, playerData);
                }
            } catch (Exception e) {
                plugin.getLogger().error("Error preloading player data for UUID " + playerUuid, e);
            }
        });
    }
}
