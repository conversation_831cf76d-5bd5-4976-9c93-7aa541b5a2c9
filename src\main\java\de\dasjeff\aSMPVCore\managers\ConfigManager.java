package de.dasjeff.aSMPVCore.managers;

import de.dasjeff.aSMPVCore.ASMPVCore;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.SafeConstructor;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * Configuration manager for Velocity plugin using SnakeYAML
 */
public class ConfigManager {

    private final ASMPVCore plugin;
    private final Path dataDirectory;
    private final Path configFile;
    private final Yaml yaml;
    private Map<String, Object> config;

    public ConfigManager(ASMPVCore plugin, Path dataDirectory) {
        this.plugin = plugin;
        this.dataDirectory = dataDirectory;
        this.configFile = dataDirectory.resolve("config.yml");
        this.yaml = new Yaml(new SafeConstructor());
        this.config = new HashMap<>();
    }

    public void loadConfig() {
        try {
            // Create data directory if it doesn't exist
            if (!Files.exists(dataDirectory)) {
                Files.createDirectories(dataDirectory);
            }

            // Create default config if it doesn't exist
            if (!Files.exists(configFile)) {
                createDefaultConfig();
            }

            // Load config from file
            try (InputStream inputStream = Files.newInputStream(configFile)) {
                config = yaml.load(inputStream);
                if (config == null) {
                    config = new HashMap<>();
                }
            }

            plugin.getLogger().info("Configuration loaded successfully.");
        } catch (IOException e) {
            plugin.getLogger().error("Failed to load configuration!", e);
            config = new HashMap<>();
        }
    }

    private void createDefaultConfig() throws IOException {
        Map<String, Object> defaultConfig = new HashMap<>();
        
        // Database configuration
        Map<String, Object> database = new HashMap<>();
        database.put("enabled", true);
        database.put("host", "localhost");
        database.put("port", 3306);
        database.put("database", "asmp_core");
        database.put("username", "root");
        database.put("password", "your_password");
        database.put("useSSL", false);
        database.put("pool_size", 10);
        defaultConfig.put("database", database);

        // Redis configuration
        Map<String, Object> redis = new HashMap<>();
        redis.put("enabled", true);
        redis.put("host", "localhost");
        redis.put("port", 6379);
        redis.put("password", "");
        redis.put("database", 0);
        redis.put("timeout", 2000);
        defaultConfig.put("redis", redis);

        // Cache configuration
        Map<String, Object> cache = new HashMap<>();
        cache.put("player_data_cache_size", 1000);
        cache.put("player_data_expire_minutes", 30);
        cache.put("enable_statistics", false);
        defaultConfig.put("cache", cache);

        // Security configuration
        Map<String, Object> security = new HashMap<>();
        security.put("command_cooldown_ms", 100);
        security.put("max_async_operations_per_player", 10);
        defaultConfig.put("security", security);

        // Write default config to file
        try (FileWriter writer = new FileWriter(configFile.toFile())) {
            yaml.dump(defaultConfig, writer);
        }

        plugin.getLogger().info("Created default configuration file.");
    }

    public void saveConfig() {
        try (FileWriter writer = new FileWriter(configFile.toFile())) {
            yaml.dump(config, writer);
            plugin.getLogger().info("Configuration saved successfully.");
        } catch (IOException e) {
            plugin.getLogger().error("Failed to save configuration!", e);
        }
    }

    public void reloadConfig() {
        loadConfig();
    }

    // Getter methods with default values
    public String getString(String path, String defaultValue) {
        return getValueFromPath(path, String.class, defaultValue);
    }

    public int getInt(String path, int defaultValue) {
        Object value = getValueFromPath(path, Object.class, defaultValue);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    public boolean getBoolean(String path, boolean defaultValue) {
        return getValueFromPath(path, Boolean.class, defaultValue);
    }

    public long getLong(String path, long defaultValue) {
        Object value = getValueFromPath(path, Object.class, defaultValue);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return defaultValue;
    }

    public double getDouble(String path, double defaultValue) {
        Object value = getValueFromPath(path, Object.class, defaultValue);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }

    @SuppressWarnings("unchecked")
    private <T> T getValueFromPath(String path, Class<T> type, T defaultValue) {
        String[] keys = path.split("\\.");
        Object current = config;

        for (String key : keys) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(key);
                if (current == null) {
                    return defaultValue;
                }
            } else {
                return defaultValue;
            }
        }

        if (type.isInstance(current)) {
            return type.cast(current);
        }
        return defaultValue;
    }

    public void set(String path, Object value) {
        setValueAtPath(path, value);
    }

    @SuppressWarnings("unchecked")
    private void setValueAtPath(String path, Object value) {
        String[] keys = path.split("\\.");
        Map<String, Object> current = config;

        for (int i = 0; i < keys.length - 1; i++) {
            String key = keys[i];
            Object next = current.get(key);
            if (!(next instanceof Map)) {
                next = new HashMap<String, Object>();
                current.put(key, next);
            }
            current = (Map<String, Object>) next;
        }

        current.put(keys[keys.length - 1], value);
    }

    public Map<String, Object> getConfigSection(String path) {
        return getValueFromPath(path, Map.class, new HashMap<>());
    }
}
