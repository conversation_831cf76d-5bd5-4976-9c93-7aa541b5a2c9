package de.dasjeff.aSMPVCore.database;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.DatabaseManager;
import de.dasjeff.aSMPVCore.model.PlayerData;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.*;
import java.util.UUID;

public class PlayerDataAccessor {

    private final ASMPVCore plugin;
    private final DatabaseManager dbManager;
    // Using the same table name as Paper core's HomeSystem for shared player data
    private final String PLAYERDATA_TABLE_NAME = "homes_playerdata";

    public PlayerDataAccessor(ASMPVCore plugin) {
        this.plugin = plugin;
        this.dbManager = plugin.getDatabaseManager();
        initializeTable();
    }

    public void initializeTable() {
        if (dbManager == null || !dbManager.isConnected()) {
            plugin.getLogger().warn("Database not connected. Cannot initialize " + PLAYERDATA_TABLE_NAME + " table from Velocity core.");
            return;
        }
        // The Paper core's HomeDataAccessor already creates this table.
        // This is a safety check or if Velocity core needs to ensure it for some reason.
        // It's generally better if one system "owns" the table creation.
        // For now, we assume the table structure is compatible and might be created by Paper.
        // If Velocity needs to ensure it, the SQL would be:
        /*
        String createPlayerDataTableSQL = "CREATE TABLE IF NOT EXISTS " + PLAYERDATA_TABLE_NAME + " (" +
                "player_uuid CHAR(36) PRIMARY KEY," +
                "last_known_name VARCHAR(16) NOT NULL COLLATE utf8mb4_general_ci," +
                "last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP," +
                "first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "INDEX idx_last_known_name (last_known_name)," +
                "INDEX idx_last_seen (last_seen)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        try (Connection conn = dbManager.getConnection(); Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createPlayerDataTableSQL);
            plugin.getLogger().info("Ensured table '" + PLAYERDATA_TABLE_NAME + "' (Velocity Core check).");
        } catch (SQLException e) {
            plugin.getLogger().error("Could not ensure " + PLAYERDATA_TABLE_NAME + " table from Velocity core!", e);
        }
        */
        plugin.getLogger().info(PLAYERDATA_TABLE_NAME + " table is expected to be managed by the Paper core plugin or exist.");
    }

    public void updatePlayerData(@NotNull UUID playerUuid, @NotNull String playerName) {
        if (dbManager == null || !dbManager.isConnected()) return;

        String sql = "INSERT INTO " + PLAYERDATA_TABLE_NAME + " (player_uuid, last_known_name, last_seen, first_seen) VALUES (?, ?, NOW(), NOW()) " +
                     "ON DUPLICATE KEY UPDATE last_known_name = VALUES(last_known_name), last_seen = NOW();";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            pstmt.setString(2, playerName);
            pstmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().error("Could not update player data for " + playerName, e);
        }
    }

    @Nullable
    public PlayerData getPlayerData(@NotNull UUID playerUuid) {
        if (dbManager == null || !dbManager.isConnected()) return null;

        String sql = "SELECT * FROM " + PLAYERDATA_TABLE_NAME + " WHERE player_uuid = ?;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return new PlayerData(
                            UUID.fromString(rs.getString("player_uuid")),
                            rs.getString("last_known_name"),
                            rs.getTimestamp("last_seen"),
                            rs.getTimestamp("first_seen")
                    );
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().error("Could not retrieve player data for UUID " + playerUuid, e);
        }
        return null;
    }

    @Nullable
    public UUID getPlayerUuidByName(@NotNull String playerName) {
        if (dbManager == null || !dbManager.isConnected()) return null;

        String sql = "SELECT player_uuid FROM " + PLAYERDATA_TABLE_NAME + " WHERE last_known_name = ? ORDER BY last_seen DESC LIMIT 1;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerName);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return UUID.fromString(rs.getString("player_uuid"));
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().error("Could not retrieve UUID for player name " + playerName, e);
        }
        return null;
    }

    @Nullable
    public String getPlayerNameByUuid(@NotNull UUID playerUuid) {
        PlayerData playerData = getPlayerData(playerUuid);
        return playerData != null ? playerData.getLastKnownName() : null;
    }
}
