package de.dasjeff.aSMPVCore.managers;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.Module;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manager for handling modular components in the Velocity core system
 */
public class ModuleManager {

    private final ASMPVCore plugin;
    private final Map<String, Module> modules = new ConcurrentHashMap<>();
    private final Map<String, Module> enabledModules = new ConcurrentHashMap<>();

    public ModuleManager(ASMPVCore plugin) {
        this.plugin = plugin;
    }

    /**
     * Registers a module with the manager
     */
    public void registerModule(@NotNull Module module) {
        String moduleName = module.getName();
        if (modules.containsKey(moduleName)) {
            plugin.getLogger().warn("Module '" + moduleName + "' is already registered. Skipping registration.");
            return;
        }

        modules.put(moduleName, module);
        plugin.getLogger().info("Registered module: " + moduleName + " v" + module.getVersion());
    }

    /**
     * Unregisters a module from the manager
     */
    public void unregisterModule(@NotNull String moduleName) {
        Module module = modules.remove(moduleName);
        if (module != null) {
            if (module.isEnabled()) {
                disableModule(moduleName);
            }
            plugin.getLogger().info("Unregistered module: " + moduleName);
        }
    }

    /**
     * Gets a module by name
     */
    @Nullable
    public Module getModule(@NotNull String moduleName) {
        return modules.get(moduleName);
    }

    /**
     * Gets all registered modules
     */
    @NotNull
    public Collection<Module> getModules() {
        return Collections.unmodifiableCollection(modules.values());
    }

    /**
     * Gets all enabled modules
     */
    @NotNull
    public Collection<Module> getEnabledModules() {
        return Collections.unmodifiableCollection(enabledModules.values());
    }

    /**
     * Checks if a module is registered
     */
    public boolean isModuleRegistered(@NotNull String moduleName) {
        return modules.containsKey(moduleName);
    }

    /**
     * Checks if a module is enabled
     */
    public boolean isModuleEnabled(@NotNull String moduleName) {
        return enabledModules.containsKey(moduleName);
    }

    /**
     * Loads all registered modules in dependency order
     */
    public void loadModules() {
        List<Module> sortedModules = sortModulesByDependencies();
        
        for (Module module : sortedModules) {
            try {
                module.onLoad(plugin);
                plugin.getLogger().info("Loaded module: " + module.getName());
            } catch (Exception e) {
                plugin.getLogger().error("Failed to load module: " + module.getName(), e);
            }
        }
    }

    /**
     * Enables all loaded modules in dependency order
     */
    public void enableModules() {
        List<Module> sortedModules = sortModulesByDependencies();
        
        for (Module module : sortedModules) {
            enableModule(module.getName());
        }
    }

    /**
     * Enables a specific module
     */
    public boolean enableModule(@NotNull String moduleName) {
        Module module = modules.get(moduleName);
        if (module == null) {
            plugin.getLogger().warn("Cannot enable module '" + moduleName + "': Module not found.");
            return false;
        }

        if (module.isEnabled()) {
            plugin.getLogger().warn("Module '" + moduleName + "' is already enabled.");
            return true;
        }

        // Check dependencies
        if (!checkDependencies(module)) {
            plugin.getLogger().error("Cannot enable module '" + moduleName + "': Missing dependencies.");
            return false;
        }

        try {
            module.onEnable(plugin);
            enabledModules.put(moduleName, module);
            plugin.getLogger().info("Enabled module: " + moduleName);
            return true;
        } catch (Exception e) {
            plugin.getLogger().error("Failed to enable module: " + moduleName, e);
            return false;
        }
    }

    /**
     * Disables all enabled modules
     */
    public void disableModules() {
        // Disable in reverse order
        List<Module> sortedModules = sortModulesByDependencies();
        Collections.reverse(sortedModules);
        
        for (Module module : sortedModules) {
            if (module.isEnabled()) {
                disableModule(module.getName());
            }
        }
    }

    /**
     * Disables a specific module
     */
    public boolean disableModule(@NotNull String moduleName) {
        Module module = enabledModules.get(moduleName);
        if (module == null) {
            plugin.getLogger().warn("Cannot disable module '" + moduleName + "': Module not enabled or not found.");
            return false;
        }

        try {
            module.onDisable(plugin);
            enabledModules.remove(moduleName);
            plugin.getLogger().info("Disabled module: " + moduleName);
            return true;
        } catch (Exception e) {
            plugin.getLogger().error("Failed to disable module: " + moduleName, e);
            return false;
        }
    }

    /**
     * Reloads a specific module
     */
    public boolean reloadModule(@NotNull String moduleName) {
        Module module = modules.get(moduleName);
        if (module == null) {
            plugin.getLogger().warn("Cannot reload module '" + moduleName + "': Module not found.");
            return false;
        }

        boolean wasEnabled = module.isEnabled();
        
        if (wasEnabled) {
            disableModule(moduleName);
        }

        try {
            module.onReload(plugin);
            plugin.getLogger().info("Reloaded module: " + moduleName);
            
            if (wasEnabled) {
                return enableModule(moduleName);
            }
            return true;
        } catch (Exception e) {
            plugin.getLogger().error("Failed to reload module: " + moduleName, e);
            return false;
        }
    }

    /**
     * Sorts modules by their dependencies using topological sort
     */
    private List<Module> sortModulesByDependencies() {
        List<Module> sorted = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        Set<String> visiting = new HashSet<>();

        for (Module module : modules.values()) {
            if (!visited.contains(module.getName())) {
                if (!topologicalSort(module, visited, visiting, sorted)) {
                    plugin.getLogger().warn("Circular dependency detected involving module: " + module.getName());
                }
            }
        }

        return sorted;
    }

    private boolean topologicalSort(Module module, Set<String> visited, Set<String> visiting, List<Module> sorted) {
        if (visiting.contains(module.getName())) {
            return false; // Circular dependency
        }
        if (visited.contains(module.getName())) {
            return true;
        }

        visiting.add(module.getName());

        // Process dependencies first
        for (String dependency : module.getDependencies()) {
            Module depModule = modules.get(dependency);
            if (depModule != null) {
                if (!topologicalSort(depModule, visited, visiting, sorted)) {
                    return false;
                }
            }
        }

        // Process soft dependencies
        for (String softDependency : module.getSoftDependencies()) {
            Module depModule = modules.get(softDependency);
            if (depModule != null) {
                if (!topologicalSort(depModule, visited, visiting, sorted)) {
                    return false;
                }
            }
        }

        visiting.remove(module.getName());
        visited.add(module.getName());
        sorted.add(module);
        return true;
    }

    /**
     * Checks if all dependencies of a module are satisfied
     */
    private boolean checkDependencies(Module module) {
        for (String dependency : module.getDependencies()) {
            if (!isModuleEnabled(dependency)) {
                plugin.getLogger().error("Module '" + module.getName() + "' requires '" + dependency + "' but it is not enabled.");
                return false;
            }
        }
        return true;
    }
}
