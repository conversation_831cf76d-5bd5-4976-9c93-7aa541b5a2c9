package de.dasjeff.aSMPVCore.modules;

import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;

/**
 * Abstract base class for modules providing common functionality
 */
public abstract class AbstractModule implements Module {

    private boolean enabled = false;
    protected ASMPVCore plugin;

    @Override
    public void onLoad(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
        plugin.getLogger().info("Loading module: " + getName() + " v" + getVersion());
    }

    @Override
    public void onEnable(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
        this.enabled = true;
        plugin.getLogger().info("Enabled module: " + getName() + " v" + getVersion());
    }

    @Override
    public void onDisable(@NotNull ASMPVCore plugin) {
        this.enabled = false;
        plugin.getLogger().info("Disabled module: " + getName());
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    @Override
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * Gets the plugin instance
     */
    protected ASMPVCore getPlugin() {
        return plugin;
    }
}
