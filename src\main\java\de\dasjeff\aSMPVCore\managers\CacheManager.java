package de.dasjeff.aSMPVCore.managers;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * Cache manager handling both local (Caffeine) and distributed (Redis) caching
 */
public class CacheManager {

    private final ASMPVCore plugin;
    private final ConcurrentHashMap<String, Cache<Object, Object>> caches = new ConcurrentHashMap<>();
    private final boolean enableStatistics;
    private JedisPool jedisPool;

    public CacheManager(ASMPVCore plugin) {
        this.plugin = plugin;
        ConfigManager config = plugin.getConfigManager();
        this.enableStatistics = config.getBoolean("cache.enable_statistics", false);
        
        initializeRedis();
        initializeDefaultCaches();
    }

    private void initializeRedis() {
        ConfigManager config = plugin.getConfigManager();
        boolean redisEnabled = config.getBoolean("redis.enabled", true);
        
        if (!redisEnabled) {
            plugin.getLogger().info("Redis is disabled in configuration.");
            return;
        }

        try {
            String host = config.getString("redis.host", "localhost");
            int port = config.getInt("redis.port", 6379);
            String password = config.getString("redis.password", "");
            int database = config.getInt("redis.database", 0);
            int timeout = config.getInt("redis.timeout", 2000);

            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(20);
            poolConfig.setMaxIdle(10);
            poolConfig.setMinIdle(2);
            poolConfig.setTestOnBorrow(true);
            poolConfig.setTestOnReturn(true);
            poolConfig.setTestWhileIdle(true);

            if (password.isEmpty()) {
                jedisPool = new JedisPool(poolConfig, host, port, timeout, null, database);
            } else {
                jedisPool = new JedisPool(poolConfig, host, port, timeout, password, database);
            }

            // Test connection
            try (var jedis = jedisPool.getResource()) {
                jedis.ping();
                plugin.getLogger().info("Successfully connected to Redis at " + host + ":" + port);
            }
        } catch (Exception e) {
            plugin.getLogger().warn("Failed to connect to Redis: " + e.getMessage());
            plugin.getLogger().warn("Continuing without Redis support.");
            jedisPool = null;
        }
    }

    private void initializeDefaultCaches() {
        ConfigManager config = plugin.getConfigManager();
        
        // Player data cache
        long playerDataCacheSize = config.getLong("cache.player_data_cache_size", 1000);
        long playerDataExpireMinutes = config.getLong("cache.player_data_expire_minutes", 30);
        
        createCache("player_data", playerDataCacheSize, playerDataExpireMinutes, TimeUnit.MINUTES);
        
        plugin.getLogger().info("Default caches initialized.");
    }

    /**
     * Creates a new Caffeine cache with the specified parameters and stores it.
     */
    @SuppressWarnings("unchecked")
    public <K, V> Cache<K, V> createCache(@NotNull String cacheName, long maximumSize, long expireAfterAccess, @NotNull TimeUnit timeUnit) {
        if (caches.containsKey(cacheName)) {
            throw new IllegalArgumentException("Cache with name '" + cacheName + "' already exists.");
        }

        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(expireAfterAccess, timeUnit);
        
        if (enableStatistics) {
            builder.recordStats();
        }
        
        Cache<Object, Object> newCache = builder.build();
        caches.put(cacheName, newCache);
        return (Cache<K, V>) newCache;
    }

    /**
     * Creates a loading cache with automatic value computation.
     */
    @SuppressWarnings("unchecked")
    public <K, V> LoadingCache<K, V> createLoadingCache(@NotNull String cacheName, long maximumSize, 
                                                       long expireAfterAccess, @NotNull TimeUnit timeUnit,
                                                       @NotNull CacheLoader<K, V> loader) {
        if (caches.containsKey(cacheName)) {
            throw new IllegalArgumentException("Cache with name '" + cacheName + "' already exists.");
        }

        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(expireAfterAccess, timeUnit);
        
        if (enableStatistics) {
            builder.recordStats();
        }
        
        LoadingCache<Object, Object> newCache = builder.build((CacheLoader<Object, Object>) loader);
        caches.put(cacheName, newCache);
        return (LoadingCache<K, V>) newCache;
    }

    /**
     * Retrieves an existing cache by name.
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public <K, V> Cache<K, V> getCache(@NotNull String cacheName) {
        return (Cache<K, V>) caches.get(cacheName);
    }

    /**
     * Safely retrieves a value from the specified cache, computing it if absent.
     */
    @Nullable
    public <K, V> V get(@NotNull String cacheName, @NotNull K key, @NotNull Function<? super K, ? extends V> mappingFunction) {
        try {
            Cache<K, V> cache = getCache(cacheName);
            if (cache == null) {
                plugin.getLogger().warn("Attempted to access non-existent cache: " + cacheName);
                return mappingFunction.apply(key); // Fallback to direct computation
            }
            return cache.get(key, mappingFunction);
        } catch (Exception e) {
            plugin.getLogger().warn("Error accessing cache '" + cacheName + "' for key: " + key, e);
            return mappingFunction.apply(key); // Fallback to direct computation
        }
    }

    /**
     * Safely puts a value into the specified cache.
     */
    public <K, V> void put(@NotNull String cacheName, @NotNull K key, @NotNull V value) {
        try {
            Cache<K, V> cache = getCache(cacheName);
            if (cache != null) {
                cache.put(key, value);
            }
        } catch (Exception e) {
            plugin.getLogger().warn("Error putting value into cache '" + cacheName + "' for key: " + key, e);
        }
    }

    /**
     * Safely removes a value from the specified cache.
     */
    public <K> void invalidate(@NotNull String cacheName, @NotNull K key) {
        try {
            Cache<K, Object> cache = getCache(cacheName);
            if (cache != null) {
                cache.invalidate(key);
            }
        } catch (Exception e) {
            plugin.getLogger().warn("Error invalidating cache '" + cacheName + "' for key: " + key, e);
        }
    }

    /**
     * Clears all entries from the specified cache.
     */
    public void invalidateAll(@NotNull String cacheName) {
        try {
            Cache<Object, Object> cache = caches.get(cacheName);
            if (cache != null) {
                cache.invalidateAll();
            }
        } catch (Exception e) {
            plugin.getLogger().warn("Error invalidating all entries in cache: " + cacheName, e);
        }
    }

    /**
     * Gets the Redis pool for direct Redis operations.
     */
    @Nullable
    public JedisPool getJedisPool() {
        return jedisPool;
    }

    /**
     * Checks if Redis is available.
     */
    public boolean isRedisAvailable() {
        return jedisPool != null && !jedisPool.isClosed();
    }

    /**
     * Shuts down all caches and Redis connection.
     */
    public void shutdown() {
        caches.clear();
        
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            plugin.getLogger().info("Redis connection pool closed.");
        }
        
        plugin.getLogger().info("CacheManager shut down.");
    }
}
