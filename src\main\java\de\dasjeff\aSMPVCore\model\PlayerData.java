package de.dasjeff.aSMPVCore.model;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.Timestamp;
import java.util.Objects;
import java.util.UUID;

/**
 * Model class representing player data shared across the network
 */
public class PlayerData {

    private final UUID playerUuid;
    private final String lastKnownName;
    private final Timestamp lastSeen;
    private final Timestamp firstSeen;

    public PlayerData(@NotNull UUID playerUuid, @NotNull String lastKnownName, 
                     @Nullable Timestamp lastSeen, @Nullable Timestamp firstSeen) {
        this.playerUuid = Objects.requireNonNull(playerUuid, "Player UUID cannot be null");
        this.lastKnownName = Objects.requireNonNull(lastKnownName, "Last known name cannot be null");
        this.lastSeen = lastSeen;
        this.firstSeen = firstSeen;
    }

    @NotNull
    public UUID getPlayerUuid() {
        return playerUuid;
    }

    @NotNull
    public String getLastKnownName() {
        return lastKnownName;
    }

    @Nullable
    public Timestamp getLastSeen() {
        return lastSeen;
    }

    @Nullable
    public Timestamp getFirstSeen() {
        return firstSeen;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlayerData that = (PlayerData) o;
        return Objects.equals(playerUuid, that.playerUuid) &&
               Objects.equals(lastKnownName, that.lastKnownName) &&
               Objects.equals(lastSeen, that.lastSeen) &&
               Objects.equals(firstSeen, that.firstSeen);
    }

    @Override
    public int hashCode() {
        return Objects.hash(playerUuid, lastKnownName, lastSeen, firstSeen);
    }

    @Override
    public String toString() {
        return "PlayerData{" +
                "playerUuid=" + playerUuid +
                ", lastKnownName='" + lastKnownName + '\'' +
                ", lastSeen=" + lastSeen +
                ", firstSeen=" + firstSeen +
                '}';
    }
}
