plugins {
    id 'java'
    id 'com.github.johnrengelman.shadow' version '7.1.2'
    id("xyz.jpenilla.run-velocity") version "2.3.1"
}

group = 'de.dasjeff'
version = '1.0.0-SNAPSHOT'

repositories {
    mavenCentral()
    maven { url = 'https://repo.papermc.io/repository/maven-public/' } // For Velocity API
}

dependencies {
    compileOnly "com.velocitypowered:velocity-api:3.3.0-SNAPSHOT"
    annotationProcessor "com.velocitypowered:velocity-api:3.3.0-SNAPSHOT"

    implementation 'redis.clients:jedis:5.1.0'
    implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8'
    implementation 'com.zaxxer:HikariCP:5.1.0'
    implementation 'org.mariadb.jdbc:mariadb-java-client:3.3.3'
    implementation 'org.yaml:snakeyaml:2.2' // For ConfigManager

    // JetBrains Annotations (optional, but good for null-safety)
    compileOnly 'org.jetbrains:annotations:24.1.0'
}

def targetJavaVersion = 17 // Velocity typically supports Java 17+
java {
    def javaVersion = JavaVersion.toVersion(targetJavaVersion)
    sourceCompatibility = javaVersion
    targetCompatibility = javaVersion
    if (JavaVersion.current() < javaVersion) {
        toolchain {
            languageVersion = JavaLanguageVersion.of(targetJavaVersion)
        }
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
    if (targetJavaVersion >= 10 || JavaVersion.current().isJava10Compatible()) {
        options.release.set(targetJavaVersion)
    }
}

processResources {
    from(sourceSets.main.resources.srcDirs) {
        filter(org.apache.tools.ant.filters.ReplaceTokens, tokens: [version: version])
    }
}

shadowJar {
    archiveBaseName.set(project.name)
    archiveClassifier.set('') // No classifier like '-all'
    archiveVersion.set(project.version)

    relocate 'redis.clients.jedis', 'de.dasjeff.aSMPVCore.lib.jedis'
    relocate 'com.github.benmanes.caffeine', 'de.dasjeff.aSMPVCore.lib.caffeine'
    relocate 'com.zaxxer.hikari', 'de.dasjeff.aSMPVCore.lib.hikaricp'
    relocate 'org.mariadb.jdbc', 'de.dasjeff.aSMPVCore.lib.mariadb'
    relocate 'org.yaml.snakeyaml', 'de.dasjeff.aSMPVCore.lib.snakeyaml'

    // Minimize JAR size by excluding unnecessary files from dependencies
    exclude 'META-INF/LICENSE*'
    exclude 'META-INF/NOTICE*'
    exclude 'META-INF/DEPENDENCIES*'
}

tasks {
    runVelocity {
        // Configure the Velocity version for our task.
        // This is the only required configuration besides applying the plugin.
        // Your plugin's jar (or shadowJar if present) will be used automatically.
        velocityVersion("3.3.0-SNAPSHOT")
    }
}

// Ensure shadowJar runs when building
tasks.build.dependsOn(shadowJar)
