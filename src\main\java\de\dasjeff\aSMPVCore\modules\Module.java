package de.dasjeff.aSMPVCore.modules;

import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;

/**
 * Interface for modular components in the Velocity core system
 */
public interface Module {

    /**
     * Gets the unique name of this module
     */
    @NotNull
    String getName();

    /**
     * Gets the version of this module
     */
    @NotNull
    String getVersion();

    /**
     * Gets the description of this module
     */
    @NotNull
    String getDescription();

    /**
     * Called when the module is being loaded
     * This is where you should initialize your module's configuration and prepare resources
     */
    void onLoad(@NotNull ASMPVCore plugin);

    /**
     * Called when the module is being enabled
     * This is where you should register listeners, commands, and start any services
     */
    void onEnable(@NotNull ASMPVCore plugin);

    /**
     * Called when the module is being disabled
     * This is where you should clean up resources, unregister listeners, and stop services
     */
    void onDisable(@NotNull ASMPVCore plugin);

    /**
     * Called when the module configuration should be reloaded
     */
    default void onReload(@NotNull ASMPVCore plugin) {
        // Default implementation does nothing
    }

    /**
     * Checks if this module is currently enabled
     */
    boolean isEnabled();

    /**
     * Sets the enabled state of this module
     */
    void setEnabled(boolean enabled);

    /**
     * Gets the dependencies of this module (other module names that must be loaded first)
     */
    @NotNull
    default String[] getDependencies() {
        return new String[0];
    }

    /**
     * Gets the soft dependencies of this module (other module names that should be loaded first if available)
     */
    @NotNull
    default String[] getSoftDependencies() {
        return new String[0];
    }
}
