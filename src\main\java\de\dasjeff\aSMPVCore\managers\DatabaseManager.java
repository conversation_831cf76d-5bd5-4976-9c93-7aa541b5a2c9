package de.dasjeff.aSMPVCore.managers;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import de.dasjeff.aSMPVCore.ASMPVCore;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * Database manager using HikariCP for connection pooling
 */
public class DatabaseManager {

    private final ASMPVCore plugin;
    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private final String password;
    private final boolean useSSL;
    private final int poolSize;

    private HikariDataSource dataSource;

    public DatabaseManager(ASMPVCore plugin) {
        this.plugin = plugin;
        ConfigManager config = plugin.getConfigManager();

        this.host = config.getString("database.host", "localhost");
        this.port = config.getInt("database.port", 3306);
        this.database = config.getString("database.database", "asmp_core");
        this.username = config.getString("database.username", "root");
        this.password = config.getString("database.password", "your_password");
        this.useSSL = config.getBoolean("database.useSSL", false);
        this.poolSize = config.getInt("database.pool_size", 10);
    }

    public boolean initialize() {
        if (!testConnection()) {
            plugin.getLogger().error("==================================================");
            plugin.getLogger().error("DATABASE CONNECTION FAILED!");
            plugin.getLogger().error("Please check your database configuration in config.yml");
            plugin.getLogger().error("Host: " + host + ":" + port);
            plugin.getLogger().error("Database: " + database);
            plugin.getLogger().error("Username: " + username);
            plugin.getLogger().error("SSL: " + useSSL);
            plugin.getLogger().error("Ensure the database server is running before enabling the plugin.");
            plugin.getLogger().error("Plugin will not enable until configured.");
            plugin.getLogger().error("==================================================");
            return false;
        }
        setupDataSource();
        // Verify connection pool is active
        if (isConnected()) {
            return true;
        } else {
            plugin.getLogger().error("DatabaseManager failed to establish a connection pool.");
            return false;
        }
    }

    private boolean testConnection() {
        String jdbcUrl = "jdbc:mariadb://" + host + ":" + port + "/" + database + "?useSSL=" + useSSL;
        try {
            HikariConfig testConfig = new HikariConfig();
            testConfig.setJdbcUrl(jdbcUrl);
            testConfig.setUsername(username);
            testConfig.setPassword(password);
            testConfig.setMaximumPoolSize(1);
            testConfig.setConnectionTimeout(5000);
            testConfig.setValidationTimeout(3000);
            testConfig.setLeakDetectionThreshold(0);

            try (HikariDataSource testDataSource = new HikariDataSource(testConfig);
                 Connection testConnection = testDataSource.getConnection()) {
                return testConnection.isValid(3);
            }
        } catch (Exception e) {
            plugin.getLogger().error("Database connection test failed: " + e.getMessage());
            return false;
        }
    }

    private void setupDataSource() {
        String jdbcUrl = "jdbc:mariadb://" + host + ":" + port + "/" + database + "?useSSL=" + useSSL;

        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(jdbcUrl);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setMaximumPoolSize(poolSize);
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setIdleTimeout(600000);
        hikariConfig.setMaxLifetime(1800000);
        hikariConfig.setLeakDetectionThreshold(60000);
        hikariConfig.setPoolName("ASMPVCore-HikariCP");

        // MariaDB specific optimizations
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");
        hikariConfig.addDataSourceProperty("useLocalSessionState", "true");
        hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true");
        hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true");
        hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true");
        hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true");
        hikariConfig.addDataSourceProperty("maintainTimeStats", "false");
        try {
            this.dataSource = new HikariDataSource(hikariConfig);
            plugin.getLogger().info("Successfully established database connection pool to " + host + ":" + port + "/");
        } catch (Exception e) {
            plugin.getLogger().error(
                    "Could not establish database connection pool! Plugin functionality requiring database access will be disabled.", e);
            this.dataSource = null;
        }
    }

    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("Database source is not available. Check plugin logs for connection errors.");
        }
        return dataSource.getConnection();
    }

    public boolean isConnected() {
        return dataSource != null && !dataSource.isClosed();
    }

    public void closeDataSource() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            plugin.getLogger().info("Database connection pool closed.");
        }
    }
}
